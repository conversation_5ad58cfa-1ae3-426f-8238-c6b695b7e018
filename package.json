{"name": "dbl-bot-advanced", "version": "2.0.0", "description": "Advanced Discord bot with Discord Bot List integration - Discord.js v14", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js --ignore logs/", "deploy": "node src/scripts/deploy-commands.js", "deploy-global": "node src/scripts/deploy-commands.js global", "deploy-guild": "node src/scripts/deploy-commands.js guild", "clear-global": "node src/scripts/deploy-commands.js clear-global", "clear-guild": "node src/scripts/deploy-commands.js clear-guild", "list-commands": "node src/scripts/deploy-commands.js list", "validate": "node src/scripts/validate-config.js", "validate-report": "node src/scripts/validate-config.js --report", "diagnose": "node src/scripts/diagnose.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["discord", "bot", "dbl", "top.gg", "advanced", "v14"], "author": "", "license": "MIT", "engines": {"node": ">=16.11.0"}, "dependencies": {"discord.js": "^14.14.1", "@top-gg/sdk": "^3.1.6", "dotenv": "^16.3.1", "chalk": "^4.1.2", "moment": "^2.29.4", "ms": "^2.1.3", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2"}}