const config = require('../config/config.js');
const logger = require('../utils/logger.js');
const { Client } = require('discord.js');

class ConfigValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
    }

    // Validate all configuration
    async validateAll() {
        logger.info('Starting configuration validation...');

        // Basic validation
        this.validateBasicConfig();
        
        // Token validation
        await this.validateDiscordToken();
        
        // API validation
        await this.validateAPIs();
        
        // Feature validation
        this.validateFeatures();
        
        // Report results
        this.reportResults();
    }

    // Validate basic configuration
    validateBasicConfig() {
        try {
            config.validate();
            logger.success('Basic configuration validation passed');
        } catch (error) {
            this.errors.push(`Basic config validation failed: ${error.message}`);
        }
    }

    // Validate Discord token by attempting connection
    async validateDiscordToken() {
        if (!config.bot.token) {
            this.errors.push('Discord token is missing');
            return;
        }

        try {
            const testClient = new Client({ intents: [] });
            
            // Set a timeout for the login attempt
            const loginPromise = testClient.login(config.bot.token);
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Login timeout')), 10000)
            );

            await Promise.race([loginPromise, timeoutPromise]);
            
            logger.success('Discord token validation passed');
            await testClient.destroy();
            
        } catch (error) {
            this.errors.push(`Discord token validation failed: ${error.message}`);
        }
    }

    // Validate external APIs
    async validateAPIs() {
        // Top.gg validation
        if (config.apis.topgg.token) {
            try {
                const response = await fetch(`https://top.gg/api/bots/${config.bot.clientId}`, {
                    headers: {
                        'Authorization': config.apis.topgg.token
                    }
                });

                if (response.ok) {
                    logger.success('Top.gg API token validation passed');
                } else {
                    this.warnings.push(`Top.gg API token may be invalid (Status: ${response.status})`);
                }
            } catch (error) {
                this.warnings.push(`Top.gg API validation failed: ${error.message}`);
            }
        } else {
            this.warnings.push('Top.gg token not provided - stats posting will be disabled');
        }
    }

    // Validate feature configuration
    validateFeatures() {
        // Check for conflicting features
        if (config.features.maintenance && config.features.autoStats) {
            this.warnings.push('Maintenance mode is enabled but auto stats posting is also enabled');
        }

        // Check webhook configuration
        if (config.features.webhooks && !config.apis.topgg.webhookAuth) {
            this.warnings.push('Webhooks are enabled but webhook auth is not configured');
        }

        // Check logging configuration
        if (config.logging.logToFile && !config.logging.logDirectory) {
            this.warnings.push('File logging is enabled but log directory is not specified');
        }

        logger.success('Feature configuration validation completed');
    }

    // Report validation results
    reportResults() {
        logger.info('\n=== Configuration Validation Results ===');
        
        if (this.errors.length === 0 && this.warnings.length === 0) {
            logger.success('✅ All validations passed! Configuration is ready.');
            return;
        }

        if (this.errors.length > 0) {
            logger.error(`❌ ${this.errors.length} error(s) found:`);
            this.errors.forEach((error, index) => {
                logger.error(`  ${index + 1}. ${error}`);
            });
        }

        if (this.warnings.length > 0) {
            logger.warn(`⚠️  ${this.warnings.length} warning(s) found:`);
            this.warnings.forEach((warning, index) => {
                logger.warn(`  ${index + 1}. ${warning}`);
            });
        }

        if (this.errors.length > 0) {
            logger.error('❌ Configuration validation failed. Please fix the errors above.');
            process.exit(1);
        } else {
            logger.success('✅ Configuration validation passed with warnings.');
        }
    }

    // Generate configuration report
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            bot: {
                hasToken: !!config.bot.token,
                hasClientId: !!config.bot.clientId,
                hasOwnerId: !!config.bot.ownerId,
                intentsCount: config.bot.intents.length,
                partialsCount: config.bot.partials.length
            },
            apis: {
                topgg: {
                    hasToken: !!config.apis.topgg.token,
                    hasWebhookAuth: !!config.apis.topgg.webhookAuth,
                    webhookPort: config.apis.topgg.webhookPort
                }
            },
            features: config.features,
            logging: config.logging,
            commands: config.commands,
            errors: this.errors,
            warnings: this.warnings
        };

        return report;
    }
}

// Main validation function
async function main() {
    const validator = new ConfigValidator();
    
    try {
        await validator.validateAll();
        
        // Generate and display report if requested
        if (process.argv.includes('--report')) {
            const report = validator.generateReport();
            console.log('\n=== Configuration Report ===');
            console.log(JSON.stringify(report, null, 2));
        }
        
    } catch (error) {
        logger.error('Validation process failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = ConfigValidator;
