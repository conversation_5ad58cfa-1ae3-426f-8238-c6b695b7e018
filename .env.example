# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_bot_client_id_here
OWNER_ID=your_discord_user_id_here
TEST_GUILD_ID=your_test_guild_id_here

# Top.gg Configuration
TOPGG_TOKEN=your_topgg_api_token_here
TOPGG_WEBHOOK_AUTH=your_topgg_webhook_auth_here
TOPGG_WEBHOOK_PORT=3000

# Database Configuration (Optional)
DATABASE_URL=your_database_url_here
DATABASE_TYPE=sqlite

# Logging Configuration
LOG_LEVEL=info
LOG_TO_FILE=false
LOG_DIRECTORY=./logs

# Command Deployment
GLOBAL_DEPLOY=false
GUILD_DEPLOY=true
DELETE_UNUSED_COMMANDS=false

# Feature Flags
AUTO_STATS=true
ENABLE_WEBHOOKS=false
ENABLE_ANALYTICS=false
MAINTENANCE_MODE=false
