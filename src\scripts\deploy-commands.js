const { REST, Routes } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../config/config.js');
const logger = require('../utils/logger.js');

class CommandDeployer {
    constructor() {
        this.commands = [];
        this.rest = new REST().setToken(config.bot.token);
    }

    // Load all commands
    async loadCommands() {
        const commandsPath = path.join(__dirname, '../commands');
        
        if (!fs.existsSync(commandsPath)) {
            throw new Error('Commands directory not found');
        }

        const categories = fs.readdirSync(commandsPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        // If no categories, load from root commands folder
        if (categories.length === 0) {
            await this.loadCommandsFromDirectory(commandsPath);
        } else {
            // Load commands from each category
            for (const category of categories) {
                const categoryPath = path.join(commandsPath, category);
                await this.loadCommandsFromDirectory(categoryPath);
            }
        }

        logger.info(`Loaded ${this.commands.length} commands for deployment`);
    }

    // Load commands from specific directory
    async loadCommandsFromDirectory(dirPath) {
        const commandFiles = fs.readdirSync(dirPath).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            try {
                const filePath = path.join(dirPath, file);
                const command = require(filePath);

                if (!command.data || !command.execute) {
                    logger.warn(`Command ${file} is missing required "data" or "execute" property`);
                    continue;
                }

                this.commands.push(command.data.toJSON());
                logger.debug(`Loaded command: ${command.data.name}`);
            } catch (error) {
                logger.error(`Failed to load command ${file}:`, error);
            }
        }
    }

    // Deploy commands globally
    async deployGlobal() {
        try {
            logger.info(`Started refreshing ${this.commands.length} global application (/) commands.`);

            const data = await this.rest.put(
                Routes.applicationCommands(config.bot.clientId),
                { body: this.commands }
            );

            logger.success(`Successfully reloaded ${data.length} global application (/) commands.`);
            return data;
        } catch (error) {
            logger.error('Error deploying global commands:', error);
            throw error;
        }
    }

    // Deploy commands to specific guild (for testing)
    async deployGuild(guildId) {
        try {
            logger.info(`Started refreshing ${this.commands.length} guild application (/) commands for guild ${guildId}.`);

            const data = await this.rest.put(
                Routes.applicationGuildCommands(config.bot.clientId, guildId),
                { body: this.commands }
            );

            logger.success(`Successfully reloaded ${data.length} guild application (/) commands for guild ${guildId}.`);
            return data;
        } catch (error) {
            logger.error(`Error deploying guild commands for ${guildId}:`, error);
            throw error;
        }
    }

    // Clear all global commands
    async clearGlobal() {
        try {
            logger.info('Clearing all global application (/) commands.');

            const data = await this.rest.put(
                Routes.applicationCommands(config.bot.clientId),
                { body: [] }
            );

            logger.success('Successfully cleared all global application (/) commands.');
            return data;
        } catch (error) {
            logger.error('Error clearing global commands:', error);
            throw error;
        }
    }

    // Clear all guild commands
    async clearGuild(guildId) {
        try {
            logger.info(`Clearing all guild application (/) commands for guild ${guildId}.`);

            const data = await this.rest.put(
                Routes.applicationGuildCommands(config.bot.clientId, guildId),
                { body: [] }
            );

            logger.success(`Successfully cleared all guild application (/) commands for guild ${guildId}.`);
            return data;
        } catch (error) {
            logger.error(`Error clearing guild commands for ${guildId}:`, error);
            throw error;
        }
    }

    // Get current deployed commands
    async getCurrentCommands(guildId = null) {
        try {
            const route = guildId 
                ? Routes.applicationGuildCommands(config.bot.clientId, guildId)
                : Routes.applicationCommands(config.bot.clientId);

            const commands = await this.rest.get(route);
            return commands;
        } catch (error) {
            logger.error('Error fetching current commands:', error);
            throw error;
        }
    }
}

// Main deployment function
async function main() {
    try {
        // Validate configuration
        config.validate();

        const deployer = new CommandDeployer();
        await deployer.loadCommands();

        const args = process.argv.slice(2);
        const action = args[0];
        const target = args[1];

        switch (action) {
            case 'global':
                await deployer.deployGlobal();
                break;
            
            case 'guild':
                if (!target) {
                    throw new Error('Guild ID required for guild deployment');
                }
                await deployer.deployGuild(target);
                break;
            
            case 'clear-global':
                await deployer.clearGlobal();
                break;
            
            case 'clear-guild':
                if (!target) {
                    throw new Error('Guild ID required for guild clearing');
                }
                await deployer.clearGuild(target);
                break;
            
            case 'list':
                const commands = await deployer.getCurrentCommands(target);
                logger.info(`Current commands (${target ? 'guild' : 'global'}):`, 
                    commands.map(cmd => cmd.name));
                break;
            
            default:
                // Default behavior based on config
                if (config.commands.globalDeploy) {
                    await deployer.deployGlobal();
                } else if (config.commands.guildDeploy && config.bot.testGuildId) {
                    await deployer.deployGuild(config.bot.testGuildId);
                } else {
                    logger.info('No deployment method specified. Use:');
                    logger.info('  node deploy-commands.js global');
                    logger.info('  node deploy-commands.js guild <guild_id>');
                    logger.info('  node deploy-commands.js clear-global');
                    logger.info('  node deploy-commands.js clear-guild <guild_id>');
                    logger.info('  node deploy-commands.js list [guild_id]');
                }
                break;
        }

    } catch (error) {
        logger.error('Deployment failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = CommandDeployer;
