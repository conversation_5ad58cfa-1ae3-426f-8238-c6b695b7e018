const config = require('../config/config.js');
const logger = require('../utils/logger.js');
const { Client } = require('discord.js');

async function diagnose() {
    logger.info('🔍 Starting bot diagnostics...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    logger.info(`Node.js version: ${nodeVersion}`);
    
    if (parseInt(nodeVersion.slice(1).split('.')[0]) < 16) {
        logger.error('❌ Node.js version 16.11.0 or higher is required!');
        return false;
    }
    
    // Check environment variables
    logger.info('📋 Checking environment variables...');
    
    const requiredVars = ['DISCORD_TOKEN', 'CLIENT_ID'];
    const missingVars = [];
    
    for (const varName of requiredVars) {
        if (!process.env[varName]) {
            missingVars.push(varName);
        } else {
            logger.success(`✅ ${varName} is set`);
        }
    }
    
    if (missingVars.length > 0) {
        logger.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
        logger.info('💡 Make sure you have a .env file with these variables set.');
        return false;
    }
    
    // Validate token format
    const token = process.env.DISCORD_TOKEN;
    if (!token.match(/^[A-Za-z0-9._-]+$/)) {
        logger.error('❌ Discord token format appears invalid');
        return false;
    }
    
    // Validate client ID format
    const clientId = process.env.CLIENT_ID;
    if (!clientId.match(/^\d{17,19}$/)) {
        logger.error('❌ Client ID format appears invalid (should be 17-19 digits)');
        return false;
    }
    
    // Test Discord connection
    logger.info('🔗 Testing Discord connection...');
    
    try {
        const testClient = new Client({ intents: [] });
        
        const loginPromise = testClient.login(token);
        const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Connection timeout after 10 seconds')), 10000)
        );
        
        await Promise.race([loginPromise, timeoutPromise]);
        logger.success('✅ Discord connection successful!');
        
        await testClient.destroy();
        
    } catch (error) {
        logger.error('❌ Discord connection failed:', error.message);
        
        if (error.code === 'TOKEN_INVALID') {
            logger.error('💡 The Discord token is invalid. Please check your token in the .env file.');
        } else if (error.code === 'DISALLOWED_INTENTS') {
            logger.error('💡 The bot is missing required intents. Enable them in Discord Developer Portal.');
        } else if (error.message.includes('timeout')) {
            logger.error('💡 Connection timed out. Check your internet connection.');
        }
        
        return false;
    }
    
    // Check file structure
    logger.info('📁 Checking file structure...');
    
    const fs = require('fs');
    const path = require('path');
    
    const requiredDirs = [
        'src/commands',
        'src/events',
        'src/handlers',
        'src/utils',
        'src/config'
    ];
    
    for (const dir of requiredDirs) {
        if (fs.existsSync(path.join(__dirname, '../../', dir))) {
            logger.success(`✅ ${dir} exists`);
        } else {
            logger.error(`❌ ${dir} is missing`);
            return false;
        }
    }
    
    // Check command files
    const commandsPath = path.join(__dirname, '../commands');
    const commandCategories = fs.readdirSync(commandsPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    
    logger.info(`📋 Found ${commandCategories.length} command categories: ${commandCategories.join(', ')}`);
    
    let totalCommands = 0;
    for (const category of commandCategories) {
        const categoryPath = path.join(commandsPath, category);
        const commandFiles = fs.readdirSync(categoryPath).filter(file => file.endsWith('.js'));
        totalCommands += commandFiles.length;
        logger.info(`  📁 ${category}: ${commandFiles.length} commands`);
    }
    
    logger.success(`✅ Total commands found: ${totalCommands}`);
    
    // Check event files
    const eventsPath = path.join(__dirname, '../events');
    const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));
    logger.success(`✅ Events found: ${eventFiles.length} (${eventFiles.map(f => f.replace('.js', '')).join(', ')})`);
    
    // Check dependencies
    logger.info('📦 Checking dependencies...');
    
    try {
        const packageJson = require('../../package.json');
        const dependencies = Object.keys(packageJson.dependencies || {});
        logger.success(`✅ Dependencies: ${dependencies.join(', ')}`);
        
        // Check if discord.js version is correct
        const discordVersion = packageJson.dependencies['discord.js'];
        if (discordVersion && discordVersion.includes('14')) {
            logger.success('✅ Discord.js v14 detected');
        } else {
            logger.warn('⚠️ Discord.js version might not be v14');
        }
        
    } catch (error) {
        logger.error('❌ Could not read package.json');
        return false;
    }
    
    logger.success('🎉 All diagnostics passed! Bot should start successfully.');
    return true;
}

// Run diagnostics if called directly
if (require.main === module) {
    diagnose().then(success => {
        if (!success) {
            logger.error('❌ Diagnostics failed. Please fix the issues above before starting the bot.');
            process.exit(1);
        } else {
            logger.success('✅ Diagnostics completed successfully!');
            process.exit(0);
        }
    }).catch(error => {
        logger.error('❌ Diagnostics crashed:', error);
        process.exit(1);
    });
}

module.exports = diagnose;
