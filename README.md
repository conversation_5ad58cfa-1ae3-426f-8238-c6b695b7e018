# 🤖 Advanced Discord Bot - Pure Slash Commands

A **professional-grade** Discord bot built with cutting-edge Discord.js v14 features. **Slash commands only** - no prefix commands. This bot showcases advanced patterns, enterprise-level architecture, and modern Discord bot development practices.

## ✨ Advanced Features

### 🏗️ **Enterprise Architecture**
- **Modular Design**: Organized handlers, utilities, and configuration systems
- **Advanced Error Handling**: Comprehensive error tracking and graceful degradation
- **Professional Logging**: Multi-level logging with file output and rotation
- **Configuration Management**: Centralized config with validation and environment support
- **Graceful Shutdown**: Proper cleanup and shutdown procedures

### 🎯 **Discord.js v14 Features**
- **Pure Slash Commands**: Modern slash command system with autocomplete - **NO PREFIX COMMANDS**
- **Context Menus**: User and message context menu commands
- **Buttons & Select Menus**: Interactive components with custom handlers
- **Modals**: Form-based interactions for complex input
- **Advanced Embeds**: Rich embed builder with templates and utilities
- **Permissions v2**: Latest Discord permissions system

### 🔧 **Developer Experience**
- **Hot Reload**: Dynamic command and event reloading
- **Command Categories**: Organized command structure
- **Cooldown System**: Per-user and global rate limiting
- **Permission Checks**: Role and permission-based command access
- **Autocomplete**: Smart command parameter suggestions
- **Validation**: Configuration and input validation

### 📊 **Production Ready**
- **Auto Stats Posting**: Automated Top.gg statistics
- **Health Monitoring**: System metrics and performance tracking
- **Scheduled Tasks**: Cron-based maintenance and updates
- **Database Ready**: Prepared for database integration
- **Webhook Support**: Top.gg webhook handling
- **Analytics**: Usage tracking and metrics

## 🎯 Commands Overview

### 📋 **General Commands**
| Command | Description | Features |
|---------|-------------|----------|
| `/help` | Interactive help system | Category selection, command search, autocomplete |

### 📊 **Information Commands**
| Command | Description | Features |
|---------|-------------|----------|
| `/stats` | Advanced bot statistics | Detailed/simple views, refresh button, system metrics |
| `/serverinfo` | Comprehensive server info | Member stats, channels, features, security settings |
| `/userinfo` | Detailed user information | Roles, permissions, status, activities, badges |
| `/vote` | Voting system | Multiple platforms, rewards info, reminders |

### 🔧 **Utility Commands**
| Command | Description | Features |
|---------|-------------|----------|
| `/ping` | Advanced latency testing | Connection quality, WebSocket status, performance rating |

## 🚀 Quick Start

### 1. **Prerequisites**
- Node.js 16.11.0 or higher
- Discord application with bot token
- (Optional) Top.gg API token

### 2. **Installation**
```bash
# Clone and install
npm install

# Copy environment template
copy .env.example .env

# Validate configuration
npm run validate

# Deploy commands (choose one)
npm run deploy          # Auto-detect deployment method
npm run deploy-guild    # Deploy to test guild (faster)

# Start the bot
npm run dev             # Development with auto-restart
npm start               # Production mode
```

### 3. **Configuration**
Edit your `.env` file with the required values:
```env
# Required
DISCORD_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_bot_client_id_here

# Recommended
OWNER_ID=your_discord_user_id_here
TEST_GUILD_ID=your_test_guild_id_here
TOPGG_TOKEN=your_topgg_api_token_here

# Optional (see .env.example for all options)
LOG_LEVEL=info
GLOBAL_DEPLOY=false
GUILD_DEPLOY=true
```

## 📁 Advanced Project Structure

```
src/
├── config/
│   └── config.js              # Centralized configuration with validation
├── handlers/
│   ├── commandHandler.js      # Advanced command handling with categories
│   └── eventHandler.js        # Modular event system
├── commands/
│   ├── general/               # General purpose commands
│   ├── info/                  # Information commands
│   └── utility/               # Utility commands
├── events/
│   ├── ready.js               # Bot startup and initialization
│   ├── interactionCreate.js   # All interaction handling
│   ├── guildCreate.js         # Server join events
│   └── error.js               # Error handling events
├── utils/
│   ├── logger.js              # Advanced logging system
│   ├── embedBuilder.js        # Rich embed templates
│   └── functions.js           # Utility functions
├── scripts/
│   ├── deploy-commands.js     # Advanced command deployment
│   └── validate-config.js     # Configuration validation
└── index.js                   # Main application entry point
```

## 🛠️ Advanced Usage

### **Command Deployment Options**
```bash
# Deploy globally (takes up to 1 hour)
node src/scripts/deploy-commands.js global

# Deploy to specific guild (instant)
node src/scripts/deploy-commands.js guild YOUR_GUILD_ID

# Clear all commands
node src/scripts/deploy-commands.js clear-global
node src/scripts/deploy-commands.js clear-guild YOUR_GUILD_ID

# List current commands
node src/scripts/deploy-commands.js list
node src/scripts/deploy-commands.js list YOUR_GUILD_ID
```

### **Configuration Validation**
```bash
# Validate all configuration
npm run validate

# Generate detailed report
node src/scripts/validate-config.js --report
```

### **Development Features**
- **Hot Reload**: Commands and events reload automatically
- **Debug Logging**: Set `LOG_LEVEL=debug` for detailed logs
- **Test Guild**: Use `TEST_GUILD_ID` for instant command updates
- **Maintenance Mode**: Set `MAINTENANCE_MODE=true` to disable for non-owners

## 🎨 Creating Advanced Commands

### **Basic Command Template**
```javascript
const { SlashCommandBuilder } = require('discord.js');
const embedBuilder = require('../../utils/embedBuilder.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('example')
        .setDescription('Example command'),

    category: 'general',
    cooldown: 5,
    permissions: ['SendMessages'],

    async execute(interaction) {
        const embed = embedBuilder.success('Success', 'Command executed!');
        await interaction.reply({ embeds: [embed] });
    }
};
```

### **Advanced Command with Components**
```javascript
const { SlashCommandBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('advanced')
        .setDescription('Advanced command with components'),

    async execute(interaction) {
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('example_button')
                    .setLabel('Click Me!')
                    .setStyle(ButtonStyle.Primary)
            );

        await interaction.reply({
            content: 'Advanced command!',
            components: [row]
        });
    }
};
```

## 🔧 Customization

### **Adding New Categories**
1. Create folder in `src/commands/`
2. Add commands to the folder
3. Commands automatically categorized

### **Custom Event Handlers**
1. Create file in `src/events/`
2. Export object with `name` and `execute` properties
3. Events automatically loaded

### **Extending Utilities**
- **Logger**: Add custom log levels and outputs
- **Embed Builder**: Create new embed templates
- **Functions**: Add utility functions for common tasks

## 🚀 Production Deployment

### **Environment Setup**
```bash
# Production environment variables
NODE_ENV=production
LOG_LEVEL=info
LOG_TO_FILE=true
GLOBAL_DEPLOY=true
AUTO_STATS=true
```

### **Process Management**
```bash
# Using PM2 (recommended)
npm install -g pm2
pm2 start src/index.js --name "dbl-bot"
pm2 startup
pm2 save

# Using Docker
docker build -t dbl-bot .
docker run -d --env-file .env dbl-bot
```

### **Monitoring**
- **Logs**: Automatic log rotation and cleanup
- **Health Checks**: Built-in system monitoring
- **Error Tracking**: Comprehensive error logging
- **Performance**: Memory and CPU usage tracking

## 🆘 Advanced Troubleshooting

### **Common Issues**
- **Commands not appearing**: Check deployment method and wait time
- **Permission errors**: Verify bot permissions and role hierarchy
- **Memory leaks**: Monitor logs for cleanup warnings
- **Rate limiting**: Check cooldown settings and API limits

### **Debug Mode**
```bash
# Enable debug logging
LOG_LEVEL=debug npm run dev

# Validate configuration
npm run validate

# Check command deployment
node src/scripts/deploy-commands.js list
```

## 📈 Performance & Scaling

- **Optimized Caching**: Efficient Discord.js cache management
- **Rate Limit Handling**: Built-in rate limit respect
- **Memory Management**: Automatic cleanup and monitoring
- **Shard Ready**: Prepared for multi-shard deployment
- **Database Ready**: Structured for database integration

## 🤝 Contributing

This bot serves as a template for advanced Discord bot development. Feel free to:
- Fork and customize for your needs
- Submit improvements and bug fixes
- Share your implementations and extensions

## 📝 License

MIT License - Use this code as a foundation for your own advanced Discord bots!
