const logger = require('../utils/logger.js');
const embedBuilder = require('../utils/embedBuilder.js');

module.exports = {
    name: 'messageCreate',
    async execute(client, message) {
        // Ignore bot messages
        if (message.author.bot) return;

        // Check if there's an active draft in this channel
        if (!client.draftManager) return;
        
        const draft = client.draftManager.getDraft(message.channelId);
        if (!draft || draft.status !== 'active') return;

        // Check if message contains a mention and a number (bid format: @user 50)
        const mentionRegex = /<@!?(\d+)>\s+(\d+)/;
        const match = message.content.match(mentionRegex);
        
        if (!match) return;

        const mentionedUserId = match[1];
        const bidAmount = parseInt(match[2]);

        // Validate bid amount
        if (isNaN(bidAmount) || bidAmount <= 0) return;

        // Check if it's the user's turn
        const currentCaptain = draft.captains[draft.currentTurn];
        if (currentCaptain !== message.author.id) {
            // Send ephemeral-like message that deletes after 5 seconds
            const errorMsg = await message.reply('❌ **Not your turn!** Please wait for your turn to bid.');
            setTimeout(() => errorMsg.delete().catch(() => {}), 5000);
            return;
        }

        try {
            // Process the bid
            const result = await client.draftManager.placeBid(draft.id, message.author.id, mentionedUserId, bidAmount);

            if (result.success) {
                // React to the message to show it was processed
                await message.react('✅');
                
                // Update the draft embed
                await client.draftManager.updateDraftEmbed(draft.id);
            } else {
                // React with error and send temporary error message
                await message.react('❌');
                const errorMsg = await message.reply(`❌ **Bid failed:** ${result.error}`);
                setTimeout(() => errorMsg.delete().catch(() => {}), 5000);
            }
        } catch (error) {
            logger.error('Draft message bid error:', error);
            await message.react('❌');
        }
    }
};
