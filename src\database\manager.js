const MongoDBHandler = require('./mongodb.js');
const logger = require('../utils/logger.js');
const config = require('../config/config.js');

class DatabaseManager {
    constructor() {
        this.mongodb = null;
        this.isInitialized = false;
    }

    // Initialize database connection
    async initialize() {
        try {
            if (this.isInitialized) {
                logger.debug('Database already initialized');
                return true;
            }

            logger.info('Initializing database connection...');

            // Check if MongoDB is configured
            if (!config.database.url || config.database.url === 'your_database_url_here') {
                logger.warn('MongoDB not configured, running without database');
                return false;
            }

            // Initialize MongoDB
            this.mongodb = new MongoDBHandler();
            const connected = await this.mongodb.connect();

            if (connected) {
                this.isInitialized = true;
                logger.success('Database initialized successfully');
                return true;
            } else {
                logger.error('Failed to initialize database');
                return false;
            }
        } catch (error) {
            logger.error('Database initialization error:', error);
            return false;
        }
    }

    // Get MongoDB instance
    getMongoDB() {
        if (!this.mongodb) {
            throw new Error('MongoDB not initialized');
        }
        return this.mongodb;
    }

    // Check if database is available
    isAvailable() {
        return this.isInitialized && this.mongodb && this.mongodb.isConnected;
    }

    // Get database status
    getStatus() {
        if (!this.isAvailable()) {
            return {
                type: 'None',
                status: 'Disconnected',
                details: 'Database not configured or connected'
            };
        }

        return {
            type: 'MongoDB',
            status: 'Connected',
            details: this.mongodb.getStatus()
        };
    }

    // Graceful shutdown
    async shutdown() {
        try {
            if (this.mongodb) {
                await this.mongodb.disconnect();
                logger.info('Database connections closed');
            }
        } catch (error) {
            logger.error('Error during database shutdown:', error);
        }
    }

    // Health check
    async healthCheck() {
        try {
            if (!this.isAvailable()) return false;
            return await this.mongodb.healthCheck();
        } catch (error) {
            logger.error('Database health check failed:', error);
            return false;
        }
    }

    // Guild operations
    async getGuild(guildId) {
        if (!this.isAvailable()) return null;
        
        try {
            return await this.mongodb.findOne('guilds', { guildId });
        } catch (error) {
            logger.error('Failed to get guild:', error);
            return null;
        }
    }

    async createGuild(guildData) {
        if (!this.isAvailable()) return null;
        
        try {
            const guild = {
                guildId: guildData.id,
                name: guildData.name,
                ownerId: guildData.ownerId,
                memberCount: guildData.memberCount,
                settings: {
                    prefix: '/',
                    language: 'en',
                    timezone: 'UTC'
                },
                features: {
                    drafts: true,
                    logging: true
                }
            };

            return await this.mongodb.insertOne('guilds', guild);
        } catch (error) {
            logger.error('Failed to create guild:', error);
            return null;
        }
    }

    async updateGuild(guildId, updateData) {
        if (!this.isAvailable()) return null;
        
        try {
            return await this.mongodb.updateOne(
                'guilds', 
                { guildId }, 
                { $set: updateData }
            );
        } catch (error) {
            logger.error('Failed to update guild:', error);
            return null;
        }
    }

    // User operations
    async getUser(userId, guildId = null) {
        if (!this.isAvailable()) return null;
        
        try {
            const query = { userId };
            if (guildId) query.guildId = guildId;
            
            return await this.mongodb.findOne('users', query);
        } catch (error) {
            logger.error('Failed to get user:', error);
            return null;
        }
    }

    async createUser(userData) {
        if (!this.isAvailable()) return null;
        
        try {
            const user = {
                userId: userData.id,
                username: userData.username,
                discriminator: userData.discriminator,
                avatar: userData.avatar,
                guildId: userData.guildId || null,
                stats: {
                    commandsUsed: 0,
                    draftsParticipated: 0,
                    teamsJoined: 0
                },
                preferences: {
                    notifications: true,
                    language: 'en'
                }
            };

            return await this.mongodb.insertOne('users', user);
        } catch (error) {
            logger.error('Failed to create user:', error);
            return null;
        }
    }

    async updateUser(userId, updateData, guildId = null) {
        if (!this.isAvailable()) return null;
        
        try {
            const query = { userId };
            if (guildId) query.guildId = guildId;
            
            return await this.mongodb.updateOne(
                'users', 
                query, 
                { $set: updateData }
            );
        } catch (error) {
            logger.error('Failed to update user:', error);
            return null;
        }
    }

    // Draft operations (for future draft system)
    async createDraft(draftData) {
        if (!this.isAvailable()) return null;
        
        try {
            const draft = {
                guildId: draftData.guildId,
                channelId: draftData.channelId,
                managerId: draftData.managerId,
                captains: draftData.captains || [],
                settings: {
                    rosterSize: draftData.rosterSize || 5,
                    budget: draftData.budget || 1000,
                    turnTime: draftData.turnTime || 30
                },
                status: 'waiting', // waiting, active, completed, cancelled
                currentTurn: 0,
                round: 1,
                teams: [],
                logs: []
            };

            return await this.mongodb.insertOne('drafts', draft);
        } catch (error) {
            logger.error('Failed to create draft:', error);
            return null;
        }
    }

    async getDraft(draftId) {
        if (!this.isAvailable()) return null;
        
        try {
            return await this.mongodb.findOne('drafts', { _id: draftId });
        } catch (error) {
            logger.error('Failed to get draft:', error);
            return null;
        }
    }

    async updateDraft(draftId, updateData) {
        if (!this.isAvailable()) return null;
        
        try {
            return await this.mongodb.updateOne(
                'drafts', 
                { _id: draftId }, 
                { $set: updateData }
            );
        } catch (error) {
            logger.error('Failed to update draft:', error);
            return null;
        }
    }

    async getActiveDrafts(guildId) {
        if (!this.isAvailable()) return [];
        
        try {
            return await this.mongodb.findMany('drafts', { 
                guildId, 
                status: { $in: ['waiting', 'active'] } 
            });
        } catch (error) {
            logger.error('Failed to get active drafts:', error);
            return [];
        }
    }

    // Logging operations
    async logAction(logData) {
        if (!this.isAvailable()) return null;
        
        try {
            const log = {
                guildId: logData.guildId,
                userId: logData.userId,
                action: logData.action,
                details: logData.details,
                timestamp: new Date(),
                metadata: logData.metadata || {}
            };

            return await this.mongodb.insertOne('logs', log);
        } catch (error) {
            logger.error('Failed to log action:', error);
            return null;
        }
    }

    // Statistics
    async getStats() {
        if (!this.isAvailable()) return null;
        
        try {
            const stats = {
                guilds: await this.mongodb.count('guilds'),
                users: await this.mongodb.count('users'),
                drafts: await this.mongodb.count('drafts'),
                activeDrafts: await this.mongodb.count('drafts', { status: 'active' }),
                completedDrafts: await this.mongodb.count('drafts', { status: 'completed' })
            };

            return stats;
        } catch (error) {
            logger.error('Failed to get stats:', error);
            return null;
        }
    }
}

// Export singleton instance
module.exports = new DatabaseManager();
