const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../utils/logger.js');
const config = require('../config/config.js');

class MongoDBHandler {
    constructor() {
        this.client = null;
        this.db = null;
        this.isConnected = false;
        this.connectionString = config.database.url;
        this.dbName = process.env.DATABASE_NAME || 'dbl_bot';
        
        // MongoDB client options
        this.clientOptions = {
            serverApi: {
                version: ServerApiVersion.v1,
                strict: true,
                deprecationErrors: true,
            },
            maxPoolSize: 10,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            family: 4
        };
    }

    // Connect to MongoDB
    async connect() {
        try {
            if (this.isConnected) {
                logger.debug('MongoDB already connected');
                return true;
            }

            logger.info('Connecting to MongoDB...');
            
            this.client = new MongoClient(this.connectionString, this.clientOptions);
            await this.client.connect();
            
            // Ping to verify connection
            await this.client.db('admin').command({ ping: 1 });
            
            this.db = this.client.db(this.dbName);
            this.isConnected = true;
            
            logger.success(`Successfully connected to MongoDB database: ${this.dbName}`);
            
            // Initialize collections
            await this.initializeCollections();
            
            return true;
        } catch (error) {
            logger.error('Failed to connect to MongoDB:', error);
            this.isConnected = false;
            return false;
        }
    }

    // Initialize required collections
    async initializeCollections() {
        try {
            const collections = [
                'drafts',
                'draft_sessions',
                'teams'
            ];

            for (const collectionName of collections) {
                const exists = await this.collectionExists(collectionName);
                if (!exists) {
                    await this.db.createCollection(collectionName);
                    logger.debug(`Created collection: ${collectionName}`);
                }
            }

            // Create indexes for better performance
            await this.createIndexes();
            
            logger.info('MongoDB collections initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize collections:', error);
        }
    }

    // Create database indexes
    async createIndexes() {
        try {
            // Guild indexes
            await this.db.collection('guilds').createIndex({ guildId: 1 }, { unique: true });
            
            // User indexes
            await this.db.collection('users').createIndex({ userId: 1 }, { unique: true });
            await this.db.collection('users').createIndex({ guildId: 1, userId: 1 });
            
            // Draft indexes
            await this.db.collection('drafts').createIndex({ guildId: 1 });
            await this.db.collection('drafts').createIndex({ status: 1 });
            await this.db.collection('drafts').createIndex({ createdAt: 1 });
            
            // Draft session indexes
            await this.db.collection('draft_sessions').createIndex({ draftId: 1 });
            await this.db.collection('draft_sessions').createIndex({ guildId: 1, status: 1 });
            
            // Team indexes
            await this.db.collection('teams').createIndex({ draftId: 1 });
            await this.db.collection('teams').createIndex({ captainId: 1 });
            
            // Player indexes
            await this.db.collection('players').createIndex({ userId: 1 });
            await this.db.collection('players').createIndex({ draftId: 1 });
            
            logger.debug('Database indexes created successfully');
        } catch (error) {
            logger.error('Failed to create indexes:', error);
        }
    }

    // Check if collection exists
    async collectionExists(name) {
        try {
            const collections = await this.db.listCollections({ name }).toArray();
            return collections.length > 0;
        } catch (error) {
            logger.error(`Failed to check collection existence: ${name}`, error);
            return false;
        }
    }

    // Disconnect from MongoDB
    async disconnect() {
        try {
            if (this.client && this.isConnected) {
                await this.client.close();
                this.isConnected = false;
                logger.info('Disconnected from MongoDB');
            }
        } catch (error) {
            logger.error('Error disconnecting from MongoDB:', error);
        }
    }

    // Get collection
    getCollection(name) {
        if (!this.isConnected || !this.db) {
            throw new Error('MongoDB not connected');
        }
        return this.db.collection(name);
    }

    // Health check
    async healthCheck() {
        try {
            if (!this.isConnected) return false;
            
            await this.client.db('admin').command({ ping: 1 });
            return true;
        } catch (error) {
            logger.error('MongoDB health check failed:', error);
            this.isConnected = false;
            return false;
        }
    }

    // Get connection status
    getStatus() {
        return {
            connected: this.isConnected,
            database: this.dbName,
            collections: this.isConnected ? 'Available' : 'Unavailable'
        };
    }

    // Generic CRUD operations
    async findOne(collection, query, options = {}) {
        try {
            return await this.getCollection(collection).findOne(query, options);
        } catch (error) {
            logger.error(`Failed to findOne in ${collection}:`, error);
            throw error;
        }
    }

    async findMany(collection, query, options = {}) {
        try {
            return await this.getCollection(collection).find(query, options).toArray();
        } catch (error) {
            logger.error(`Failed to findMany in ${collection}:`, error);
            throw error;
        }
    }

    async insertOne(collection, document) {
        try {
            const result = await this.getCollection(collection).insertOne({
                ...document,
                createdAt: new Date(),
                updatedAt: new Date()
            });
            return result;
        } catch (error) {
            logger.error(`Failed to insertOne in ${collection}:`, error);
            throw error;
        }
    }

    async insertMany(collection, documents) {
        try {
            const now = new Date();
            const documentsWithTimestamps = documents.map(doc => ({
                ...doc,
                createdAt: now,
                updatedAt: now
            }));
            
            const result = await this.getCollection(collection).insertMany(documentsWithTimestamps);
            return result;
        } catch (error) {
            logger.error(`Failed to insertMany in ${collection}:`, error);
            throw error;
        }
    }

    async updateOne(collection, query, update, options = {}) {
        try {
            const result = await this.getCollection(collection).updateOne(
                query, 
                { 
                    ...update, 
                    $set: { 
                        ...update.$set, 
                        updatedAt: new Date() 
                    } 
                }, 
                options
            );
            return result;
        } catch (error) {
            logger.error(`Failed to updateOne in ${collection}:`, error);
            throw error;
        }
    }

    async updateMany(collection, query, update, options = {}) {
        try {
            const result = await this.getCollection(collection).updateMany(
                query, 
                { 
                    ...update, 
                    $set: { 
                        ...update.$set, 
                        updatedAt: new Date() 
                    } 
                }, 
                options
            );
            return result;
        } catch (error) {
            logger.error(`Failed to updateMany in ${collection}:`, error);
            throw error;
        }
    }

    async deleteOne(collection, query) {
        try {
            const result = await this.getCollection(collection).deleteOne(query);
            return result;
        } catch (error) {
            logger.error(`Failed to deleteOne in ${collection}:`, error);
            throw error;
        }
    }

    async deleteMany(collection, query) {
        try {
            const result = await this.getCollection(collection).deleteMany(query);
            return result;
        } catch (error) {
            logger.error(`Failed to deleteMany in ${collection}:`, error);
            throw error;
        }
    }

    // Upsert operation
    async upsert(collection, query, update) {
        try {
            const result = await this.updateOne(collection, query, update, { upsert: true });
            return result;
        } catch (error) {
            logger.error(`Failed to upsert in ${collection}:`, error);
            throw error;
        }
    }

    // Count documents
    async count(collection, query = {}) {
        try {
            return await this.getCollection(collection).countDocuments(query);
        } catch (error) {
            logger.error(`Failed to count in ${collection}:`, error);
            throw error;
        }
    }

    // Aggregate operation
    async aggregate(collection, pipeline) {
        try {
            return await this.getCollection(collection).aggregate(pipeline).toArray();
        } catch (error) {
            logger.error(`Failed to aggregate in ${collection}:`, error);
            throw error;
        }
    }
}

module.exports = MongoDBHandler;
