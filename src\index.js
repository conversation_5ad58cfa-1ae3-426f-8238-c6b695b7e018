const { Client } = require('discord.js');
const config = require('./config/config.js');
const logger = require('./utils/logger.js');
const CommandHandler = require('./handlers/commandHandler.js');
const EventHandler = require('./handlers/eventHandler.js');
const AdvancedInteractionHandler = require('./handlers/interactionHandler.js');
const databaseManager = require('./database/manager.js');

// Validate configuration
try {
    config.validate();
    logger.success('Configuration validated successfully');
} catch (error) {
    logger.error('Configuration validation failed:', error);
    process.exit(1);
}

// Create Discord client with advanced configuration
const client = new Client({
    intents: config.bot.intents,
    partials: config.bot.partials,
    allowedMentions: {
        parse: ['users', 'roles'],
        repliedUser: false
    },
    rest: {
        timeout: 15000,
        retries: 3
    }
});

// Initialize handlers
client.commandHandler = new CommandHandler(client);
client.eventHandler = new EventHandler(client);
client.interactionHandler = new AdvancedInteractionHandler(client);

// Add utility references to client
client.config = config;
client.logger = logger;

// Initialize bot
async function initializeBot() {
    try {
        logger.info(`Starting bot initialization... (PID: ${process.pid})`);

        // Initialize database
        logger.info('Initializing database...');
        const dbConnected = await databaseManager.initialize();
        if (dbConnected) {
            client.database = databaseManager;
            logger.success('Database connected successfully');
        } else {
            logger.warn('Bot starting without database connection');
        }

        // Load commands and events
        await client.commandHandler.loadCommands();
        await client.eventHandler.loadEvents();

        // Auto-deploy commands if enabled
        if (config.commands.guildDeploy && config.bot.testGuildId) {
            logger.info('Auto-deploying guild commands...');
            const CommandDeployer = require('./scripts/deploy-commands.js');
            const deployer = new CommandDeployer();
            await deployer.loadCommands();
            await deployer.deployGuild(config.bot.testGuildId);
        } else if (config.commands.globalDeploy) {
            logger.info('Auto-deploying global commands...');
            const CommandDeployer = require('./scripts/deploy-commands.js');
            const deployer = new CommandDeployer();
            await deployer.loadCommands();
            await deployer.deployGlobal();
        }

        // Setup global error handlers
        setupErrorHandlers();

        // Setup graceful shutdown
        setupGracefulShutdown();

        // Check if token exists
        if (!config.bot.token) {
            throw new Error('Discord token is missing. Please set DISCORD_TOKEN in your .env file.');
        }

        // Login to Discord
        logger.info('Connecting to Discord...');
        await client.login(config.bot.token);

    } catch (error) {
        logger.error('Failed to initialize bot:', error);

        // Provide more specific error information
        if (error.code === 'TOKEN_INVALID') {
            logger.error('Invalid Discord token provided. Please check your DISCORD_TOKEN in .env file.');
        } else if (error.code === 'DISALLOWED_INTENTS') {
            logger.error('Bot is missing required intents. Please enable them in Discord Developer Portal.');
        } else if (error.message) {
            logger.error('Error details:', error.message);
        }

        process.exit(1);
    }
}

// Setup global error handlers
function setupErrorHandlers() {
    process.on('unhandledRejection', (reason, promise) => {
        logger.error('Unhandled Rejection at:', { promise, reason });
    });

    process.on('uncaughtException', (error) => {
        logger.error('Uncaught Exception:', error);
        process.exit(1);
    });

    process.on('warning', (warning) => {
        logger.warn('Process Warning:', warning);
    });
}

// Setup graceful shutdown
function setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
        logger.info(`Received ${signal}. Starting graceful shutdown...`);

        try {
            // Close Discord connection
            if (client.readyAt) {
                await client.destroy();
                logger.info('Discord client destroyed');
            }

            // Close database connection
            if (client.database) {
                await client.database.shutdown();
                logger.info('Database connection closed');
            }

            // Perform cleanup tasks
            logger.info('Cleanup completed');

            process.exit(0);
        } catch (error) {
            logger.error('Error during shutdown:', error);
            process.exit(1);
        }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
}

// Start the bot
initializeBot();